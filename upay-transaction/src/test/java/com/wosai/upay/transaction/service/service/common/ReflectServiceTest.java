package com.wosai.upay.transaction.service.service.common;

import com.google.common.cache.LoadingCache;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.util.CommonUtil;
import com.wosai.upay.transaction.util.CryptoUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.security.Key;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"javax.management.*"})
@PrepareForTest({CryptoUtil.class, CommonUtil.class})
public class ReflectServiceTest {

    @InjectMocks
    private ReflectService reflectService;

    @Mock
    private LoadingCache<String, String> rsaKeyCache;

    @Before
    public void setUp() throws Exception {
        // 注入mock的rsaKeyCache
        Whitebox.setInternalState(reflectService, "rsaKeyCache", rsaKeyCache);

        // Mock rsaKeyCache.get方法
        Mockito.when(rsaKeyCache.get(anyString())).thenReturn("mocked-rsa-key");

        // Mock静态方法
        PowerMockito.mockStatic(CryptoUtil.class);
        PowerMockito.mockStatic(CommonUtil.class);
        PowerMockito.when(CryptoUtil.decrypt(Mockito.any(), (String) Mockito.any())).thenReturn("13800138000");
        PowerMockito.when(CommonUtil.maskPhoneNumber(Mockito.any())).thenReturn("138****8000");
    }

    @Test
    public void testConvertReflect2() throws  Exception {
        System.out.println(rsaKeyCache.get("sww"));
    }

    @Test
    public void testConvertReflect_NonString() {
        // 测试非字符串输入
        Object result = reflectService.convertReflect("partydeposit");
        assertEquals("partydeposit", result);
    }

    @Test
    public void testConvertReflect_WithDepositType() {
        // 构造带有储值类型的reflect字符串
        String reflect = "{\"" + Transaction.REFLECT_DEPOSIT_TYPE_KEY + "\":\"" +
                Transaction.REFLECT_DEPOSIT_TYPE + "\",\"" +
                Transaction.REFLECT_DEPOSIT_CELLPHONE + "\":\"13800138000\"}";

        Object result = reflectService.convertReflect(reflect);
        assertTrue(result instanceof String);
        assertTrue(((String) result).contains("138****8000"));
    }

    @Test
    public void testGetReflectRemark() {
        // 构造带有备注的reflect字符串
        String reflect = "{\"" + Transaction.REFLECT_DEPOSIT_TYPE_KEY + "\":\"" +
                Transaction.REFLECT_DEPOSIT_TYPE + "\",\"" +
                Transaction.REFLECT_DEPOSIT_REMARK + "\":\"测试备注\"}";

        Object result = reflectService.getReflectRemark(reflect);
        assertEquals("测试备注", result);
    }

    @Test
    public void testGetReflectRemark_NonString() {
        // 测试非字符串输入
        Object result = reflectService.getReflectRemark(123);
        assertEquals(123, result);
    }
}
