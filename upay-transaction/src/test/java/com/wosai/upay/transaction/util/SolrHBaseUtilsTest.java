package com.wosai.upay.transaction.util;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.transaction.constant.CommonConstant;
import org.apache.hadoop.hbase.util.Bytes;
import org.junit.Assert;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

public class SolrHBaseUtilsTest {

    @Test
    public void parseReflectField1() {
        byte[] reflectValue = null;
        Object result = SolrHBaseUtils.parseReflectField(reflectValue);
        Assert.assertNull("单元测试失败", result);
    }

    @Test
    public void parseReflectField2() {
        Map<String, Object> map = new HashMap<String, Object>() {{
            put("name", "name is 名称");
            put("age", 16);
            put("timestamp", 1614936048294L);
        }};

        byte[] reflectValue = Bytes.toBytes(JSON.toJSONString(map));
        Object result = SolrHBaseUtils.parseReflectField(reflectValue);
        Assert.assertTrue("单元测试失败", result instanceof Map);
        Assert.assertEquals("单元测试失败", "name is 名称", ((Map) result).get("name"));
        Assert.assertEquals("单元测试失败", 16, ((Map) result).get("age"));
        Assert.assertEquals("单元测试失败", 1614936048294L, ((Map) result).get("timestamp"));
    }

    @Test
    public void parseReflectField3() {
        String text = "蚂蚁嘿";
        byte[] reflectValue = Bytes.toBytes(text);
        Object result = SolrHBaseUtils.parseReflectField(reflectValue);
        Assert.assertTrue("单元测试失败", result instanceof String);
        Assert.assertEquals("单元测试失败", text, result);
    }

    @Test
    public void parseReflectField14() {
        String text = "{\"蚂蚁嘿\"}";
        byte[] reflectValue = Bytes.toBytes(text);
        Object result = SolrHBaseUtils.parseReflectField(reflectValue);
        Assert.assertTrue("单元测试失败", result instanceof String);
        Assert.assertEquals("单元测试失败", text, result);
    }

}
