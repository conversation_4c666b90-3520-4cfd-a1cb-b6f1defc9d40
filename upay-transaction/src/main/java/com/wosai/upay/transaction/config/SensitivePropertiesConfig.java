package com.wosai.upay.transaction.config;

import com.wosai.pay.common.sensitive.apollo.SensitivePropertiesLoader;
import com.wosai.upay.transaction.model.SensitiveProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SensitivePropertiesConfig {
    @Bean
    public SensitiveProperties sensitiveProperties() {
        return SensitivePropertiesLoader.getSensitiveProperties(SensitiveProperties.class);
    }
}