package com.wosai.upay.transaction.config;

import com.wosai.upay.transaction.model.SensitiveProperties;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.util.Properties;

@Configuration
public class JavaMailSenderConfig {

    @Value("${mail.host}")
    private String host;

    @Value("${mail.port}")
    private int port;

    @Value("${mail.protocol}")
    private String protocol;

    @Value("${mail.properties.default-encoding}")
    private String defaultEncoding;

    @Value("${mail.properties.mail.smtp.auth}")
    private boolean smtpAuth;

    @Value("${mail.properties.mail.smtp.starttls.enable}")
    private boolean starttlsEnable;

    @Value("${mail.properties.mail.smtp.quitwait}")
    private boolean smtpQuitwait;

    @Value("${mail.properties.mail.smtp.ssl.enable}")
    private boolean sslEnable;

    @Value("${mail.properties.mail.imap.ssl.socketFactory.fallback}")
    private boolean socketFactoryFallback;

    @Value("${mail.properties.mail.smtp.ssl.socketFactory.class}")
    private String sslSocketFactoryClass;

    @Value("${mail.properties.mail.smtp.connectiontimeout}")
    private int connectionTimeout;

    @Value("${mail.properties.mail.smtp.timeout}")
    private int timeout;

    @Value("${mail.properties.mail.smtp.writetimeout}")
    private int writeTimeout;

    @Value("${mail.properties.mail.debug}")
    private boolean mailDebug;

    @Bean
    public JavaMailSender javaMailSender(SensitiveProperties sensitiveProperties) {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();

        // 设置主机、端口、用户名和密码
        mailSender.setHost(host);
        mailSender.setPort(port);
        mailSender.setUsername(sensitiveProperties.getMailUsername());
        mailSender.setPassword(sensitiveProperties.getMailPassword());

        // 设置协议和默认编码
        mailSender.setProtocol(protocol);
        mailSender.setDefaultEncoding(defaultEncoding);

        // JavaMail 属性配置
        Properties javaMailProperties = new Properties();
        javaMailProperties.put("mail.smtp.auth", smtpAuth);
        javaMailProperties.put("mail.smtp.starttls.enable", starttlsEnable);
        javaMailProperties.put("mail.smtp.quitwait", smtpQuitwait);
        javaMailProperties.put("mail.smtp.ssl.enable", sslEnable);
        javaMailProperties.put("mail.imap.ssl.socketFactory.fallback", socketFactoryFallback);
        javaMailProperties.put("mail.smtp.ssl.socketFactory.class", sslSocketFactoryClass);
        javaMailProperties.put("mail.smtp.connectiontimeout", connectionTimeout);
        javaMailProperties.put("mail.smtp.timeout", timeout);
        javaMailProperties.put("mail.smtp.writetimeout", writeTimeout);
        javaMailProperties.put("mail.debug", mailDebug);

        mailSender.setJavaMailProperties(javaMailProperties);

        return mailSender;
    }
}
