package com.wosai.upay.transaction.service.service.common;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.wosai.data.bean.BeanUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.util.CommonUtil;
import com.wosai.upay.transaction.util.CryptoUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description: This is a description of the class.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023/11/23
 */
@Service
public class ReflectService {
    public static final Logger logger = LoggerFactory.getLogger(ReflectService.class);
    @Resource
    private SupportService supportService;

    @Value("${config.deposit.reflect.rsa_key.id}")
    private String depositRsaKeyId;

    private final LoadingCache<String, String> rsaKeyCache = CacheBuilder.newBuilder()
            .expireAfterAccess(5, TimeUnit.MINUTES)
            .maximumSize(10)
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(String rsaKeyId) {
                    return supportService.getRsaKeyDataById(rsaKeyId);
                }
            });

    public Object convertReflect(Object reflect) {
        if (reflect instanceof String) {
            if (!org.apache.commons.lang.StringUtils.contains((String) reflect, Transaction.REFLECT_DEPOSIT_TYPE)) {
                return reflect;
            }
            Map<String, Object> map = convertReflectMap((String) reflect);
            return Objects.isNull(map) ? reflect : JacksonUtil.toJsonString(map);
        }
        return reflect;
    }

    public Object getReflectRemark(Object reflect) {
        if (reflect instanceof String) {
            if (!org.apache.commons.lang.StringUtils.contains((String) reflect, Transaction.REFLECT_DEPOSIT_TYPE)) {
                return reflect;
            }
            Map<String, Object> map = convertReflectMap((String) reflect);
            return MapUtil.getObject(map, Transaction.REFLECT_DEPOSIT_REMARK);
        }
        return reflect;
    }

    private Map<String, Object> convertReflectMap(String reflect) {
        if (StringUtils.isEmpty(reflect)) {
            return null;
        }
        String originReflect = reflect;
        reflect = reflect
                .replace("\\", "")
                .replace("\"{", "{")
                .replace("}\"", "}");
        Map<String, Object> reflectMap;
        try {
            reflectMap = JacksonUtil.toBean(reflect, Map.class);
            if (MapUtil.isEmpty(reflectMap)) {
                return null;
            }
            // 非预授权不处理
            String type = MapUtil.getString(reflectMap, Transaction.REFLECT_DEPOSIT_TYPE_KEY);
            if (!Objects.equals(type, Transaction.REFLECT_DEPOSIT_TYPE)) {
                return  null;
            }
        } catch (Exception e) {
            logger.error("JSON反序列化失败. reflect:{}", originReflect);
            return null;
        }
        String cellphone = MapUtil.getString(reflectMap, Transaction.REFLECT_DEPOSIT_CELLPHONE);
        if (org.apache.commons.lang.StringUtils.isEmpty(cellphone)) {
            return reflectMap;
        }
        try {
            String rsaKey = rsaKeyCache.get(depositRsaKeyId);
            cellphone = CryptoUtil.decrypt(cellphone, rsaKey);
            cellphone = CommonUtil.maskPhoneNumber(cellphone);
            BeanUtil.setProperty(reflectMap, Transaction.REFLECT_DEPOSIT_CELLPHONE, cellphone);
        } catch (Exception e) {
            logger.error("decrypt cellphone filed. reflect:{}", originReflect);
        }
        return reflectMap;
    }
}
