package com.wosai.upay.transaction.config;

import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import com.wosai.core.crypto.client.CryptoClient;
import com.wosai.core.crypto.service.CryptoService;
import com.wosai.upay.core.model.SensitiveProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class RpcConfig {

    <bean name="/rpc/terminal" class="com.googlecode.jsonrpc4j.spring.JsonServiceExporter">
        <property name="service" ref="backendUpayTerminalService"/>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.BackendUpayTerminalService"/>
        <property name="errorResolver" ref="rpcErrorResolver"></property>
    </bean>

    <bean id="qrcodeImagedown" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-picture.server}rpc/image"></property>
        <property name="serviceInterface" value="com.wosai.upay.picture.service.ImageBuildService"></property>
        <property name="serverName" value="upay-picture-service"/>
        <property name="connectionTimeoutMillis" value="${upay-picture.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${upay-picture.read_timeout:5000}"/>
    </bean>

    <bean id="prepaidStatementService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-prepaid-card.server}rpc/statement"></property>
        <property name="serviceInterface" value="com.wosai.upay.prepaid.api.PrepaidStatementService"></property>
        <property name="serverName" value="upay-prepaid-card.server"/>
        <property name="connectionTimeoutMillis" value="${upay-picture.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${upay-picture.read_timeout:5000}"/>
    </bean>

    <bean id="prepaidIssuerService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-prepaid-card.server}rpc/issuer"></property>
        <property name="serviceInterface" value="com.wosai.upay.prepaid.api.PrepaidIssuerService"></property>
        <property name="serverName" value="upay-prepaid-card.server"/>
        <property name="connectionTimeoutMillis" value="${upay-picture.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${upay-picture.read_timeout:5000}"/>
    </bean>


    <bean id="prepaidMerchantService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-prepaid-card.server}rpc/merchant"></property>
        <property name="serviceInterface" value="com.wosai.upay.prepaid.api.PrepaidMerchantService"></property>
        <property name="serverName" value="upay-prepaid-card.server"/>
        <property name="connectionTimeoutMillis" value="${upay-picture.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${upay-picture.read_timeout:5000}"/>
    </bean>

    <bean id="merchantUserService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${merchant-user-service.server}/rpc/merchantuser"></property>
        <property name="serviceInterface" value="com.wosai.app.service.MerchantUserService"></property>
        <property name="serverName" value="merchant-user-service"/>
        <property name="connectionTimeoutMillis" value="${merchant-user-service.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${merchant-user-service.read_timeout:5000}"/>
    </bean>


    <bean id="externalMerchantService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}rpc/merchant"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.MerchantService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${core-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${core-business.read_timeout:5000}"/>
    </bean>

    <bean id="externalTradeConfigService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}rpc/tradeConfig"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.TradeConfigService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${core-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${core-business.read_timeout:5000}"/>
    </bean>

    <bean id="externalBusinssCommonService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}rpc/common"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.BusinssCommonService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${core-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${core-business.read_timeout:5000}"/>
    </bean>


    <bean id="externalStoreService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}rpc/store"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.StoreService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${core-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${core-business.read_timeout:5000}"/>
    </bean>

    <bean id="merchantCenterStoreService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${merchant-center.server}rpc/store"></property>
        <property name="serviceInterface" value="com.wosai.mc.service.StoreService"></property>
        <property name="serverName" value="merchant-center"/>
        <property name="connectionTimeoutMillis" value="${merchant-center.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${merchant-center.read_timeout:5000}"/>
    </bean>

    <bean id="externalTerminalService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}rpc/terminal"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.TerminalService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${core-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${core-business.read_timeout:5000}"/>
    </bean>

    <bean id="externalMetaService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}rpc/meta"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.MetaService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${core-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${core-business.read_timeout:5000}"/>
    </bean>

    <bean id="externalChangeShiftsService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}rpc/changeShifts"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.ChangeShiftsService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${core-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${core-business.read_timeout:5000}"/>
    </bean>

    <bean id="supportService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}rpc/support"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.SupportService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${core-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${core-business.read_timeout:5000}"/>
    </bean>

    <bean id="merchantUserGroupService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${merchant-user-service.server}rpc/group"></property>
        <property name="serviceInterface" value="com.wosai.app.service.GroupService"></property>
        <property name="serverName" value="merchant-user-service"/>
        <property name="connectionTimeoutMillis" value="${merchant-user-service.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${merchant-user-service.read_timeout:5000}"/>
    </bean>

    <bean id="merchantUserDepartmentService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${merchant-user-service.server}rpc/department"></property>
        <property name="serviceInterface" value="com.wosai.app.service.DepartmentService"></property>
        <property name="serverName" value="merchant-user-service"/>
        <property name="connectionTimeoutMillis" value="${merchant-user-service.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${merchant-user-service.read_timeout:5000}"/>
    </bean>

<!--    upay-task-center-api -->

    <bean id="taskExportService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-task-center.server}rpc/export"></property>
        <property name="serviceInterface" value="com.wosai.upay.task.center.service.ExportService"></property>
        <property name="serverName" value="upay-task-center"/>
        <property name="connectionTimeoutMillis" value="${upay-trade.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${upay-trade.read_timeout:10000}"/>
    </bean>

    <bean id="taskTaskLogService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-task-center.server}rpc/task"></property>
        <property name="serviceInterface" value="com.wosai.upay.task.center.service.TaskLogService"></property>
        <property name="serverName" value="upay-task-center"/>
        <property name="connectionTimeoutMillis" value="${upay-trade.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${upay-trade.read_timeout:10000}"/>
    </bean>


    <bean id="taskStatementObjectConfigService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-task-center.server}rpc/statementObjectConfig"></property>
        <property name="serviceInterface" value="com.wosai.upay.task.center.service.StatementObjectConfigService"></property>
        <property name="serverName" value="upay-task-center"/>
        <property name="connectionTimeoutMillis" value="${upay-trade.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${upay-trade.read_timeout:10000}"/>
    </bean>



    <bean id = "formService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-trade.server}rpc/form"></property>
        <property name="serviceInterface" value="com.wosai.upay.trade.api.FormService"></property>
        <property name="serverName" value="upay-trade"/>
        <property name="connectionTimeoutMillis" value="${upay-trade.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${upay-trade.read_timeout:10000}"/>
    </bean>

    <bean id = "formpayService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-trade.server}rpc/formpay"></property>
        <property name="serviceInterface" value="com.wosai.upay.trade.api.FormpayService"></property>
        <property name="serverName" value="upay-trade"/>
        <property name="connectionTimeoutMillis" value="${upay-trade.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${upay-trade.read_timeout:10000}"/>
    </bean>

    <bean id = "cashDeskService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}rpc/cash_desk"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.CashDeskService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${core-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${core-business.read_timeout:5000}"/>
    </bean>

    <bean id = "cashDeskTradeService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-cashdesk.server}rpc/cashdesk"></property>
        <property name="serviceInterface" value="com.wosai.upay.cashdesk.api.service.CashDeskTradeService"></property>
        <property name="serverName" value="upay-cashdesk"/>
        <property name="connectionTimeoutMillis" value="500"/>
        <property name="readTimeoutMillis" value="5000"/>
    </bean>

    <bean id = "appMerchantServie" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${app-backend.server}rpc/merchantconfig"></property>
        <property name="serviceInterface" value="com.wosai.app.backend.api.service.IMerchantService"></property>
        <property name="serverName" value="app-backend-process"/>
        <property name="connectionTimeoutMillis" value="${app-backend.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${app-backend.read_timeout:5000}"/>
    </bean>

    <bean id="appStaffService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${app-backend.server}rpc/staff"></property>
        <property name="serviceInterface" value="com.wosai.app.backend.api.service.IStaffService"></property>
        <property name="serverName" value="app-backend-service"/>
        <property name="connectionTimeoutMillis" value="${app-backend.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${app-backend.read_timeout:5000}"/>
    </bean>

    <bean id="merchantUserServiceV2" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${merchant-user-service.server}/rpc/merchantuserV2"></property>
        <property name="serviceInterface" value="com.wosai.app.service.v2.MerchantUserServiceV2"></property>
        <property name="serverName" value="merchant-user-service"/>
        <property name="connectionTimeoutMillis" value="${merchant-user-service.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${merchant-user-service.read_timeout:5000}"/>
    </bean>

    <bean id = "merchantUserDataPermissionsService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${merchant-user-service.server}rpc/merchantUser/dataPermissions"></property>
        <property name="serviceInterface" value="com.wosai.app.service.MerchantUserDataPermissionsService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${merchant-user-service.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${merchant-user-service.read_timeout:5000}"/>
    </bean>

    <bean id="nexusAccountService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${rpc.service.url.ads.coeus.service}rpc/nexus/account"></property>
        <property name="serviceInterface" value="com.wosai.ads.coeus.api.service.NexusAccountService"></property>
        <property name="serverName" value="ads-coeus"/>
        <property name="connectionTimeoutMillis" value="${rpc.service.url.ads.coeus.service.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${rpc.service.url.ads.coeus.service.read_timeout:5000}"/>
    </bean>


    <bean id="receiverService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${profit-sharing.server}rpc/receiver"></property>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.remote.RemoteReceiverService"></property>
        <property name="serverName" value="profit-sharing"/>
        <property name="connectionTimeoutMillis" value="${profit-sharing.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${profit-sharing.read_timeout:60000}"/>
    </bean>

    <bean id="withdrawService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${shouqianba-withdraw-service.server}/rpc/withdraw"></property>
        <property name="serviceInterface" value="com.wosai.shouqianba.withdrawservice.service.WithdrawService"></property>
        <property name="serverName" value="shouqianba-withdraw-service"/>
        <property name="connectionTimeoutMillis" value="${shouqianba-withdraw-service.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${shouqianba-withdraw-service.read_timeout:60000}"/>
    </bean>
    <bean id="clearanceService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${clearance-service.server}/rpc/clearance"/>
        <property name="serviceInterface" value="com.wosai.upay.clearance.service.ClearanceService"/>
        <property name="serverName" value="clearance-service"/>
        <property name="connectionTimeoutMillis" value="${clearance-service.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${clearance-service.read_timeout:60000}"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImplExporter">
        <property name="objectMapper" ref="myObjectMapper"/>
        <property name="errorResolver" ref="rpcErrorResolver"/>
        <property name="interceptorList" ref="jsonRpcSentinelInterceptor"/>
    </bean>

    <bean id = "merchantGrayService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-grayscale.server}rpc/merchantGray"></property>
        <property name="serviceInterface" value="com.wosai.service.IMerchantGrayService"></property>
        <property name="serverName" value="upay-grayscale"/>
        <property name="connectionTimeoutMillis" value="${upay-grayscale.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${upay-grayscale.read_timeout:3000}"/>
    </bean>

    <bean id = "customerService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${marketing-saas-merchant-facade.server}rpc/customer"></property>
        <property name="serviceInterface" value="com.wosai.market.saas.merchant.api.service.CustomerService"></property>
        <property name="serverName" value="marketing-saas-merchant-facade"/>
        <property name="connectionTimeoutMillis" value="${marketing-saas-merchant-facade.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${marketing-saas-merchant-facade.read_timeout:500}"/>
    </bean>

    <bean id = "accountReportService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${transaction-report.server}rpc/account_report_proxy_v2"></property>
        <property name="serviceInterface" value="com.wosai.service.IAccountReportServiceProxyV2"></property>
        <property name="serverName" value="transaction-report"/>
        <property name="connectionTimeoutMillis" value="${transaction-report.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${transaction-report.read_timeout:6000}"/>
    </bean>


    <bean id = "tradeComboService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${trade_manage.server}rpc/trade_combo_detail"></property>
        <property name="serviceInterface" value="com.wosai.trade.service.TradeComboDetailService"></property>
        <property name="serverName" value="trade_manage"/>
        <property name="connectionTimeoutMillis" value="${trade_manage.server.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${trade_manage.server.read_timeout:6000}"/>
    </bean>

    <bean id = "tradeAppService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${trade_manage.server}rpc/trade_app"></property>
        <property name="serviceInterface" value="com.wosai.trade.service.TradeAppService"></property>
        <property name="serverName" value="trade_manage"/>
        <property name="connectionTimeoutMillis" value="${trade_manage.server.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${trade_manage.server.read_timeout:6000}"/>
    </bean>


    <bean id = "metaService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}rpc/meta"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.MetaService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${core-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${core-business.read_timeout:5000}"/>
    </bean>

    <bean id = "t9Service" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${merchant-contract-job.server}/rpc/t9"></property>
        <property name="serviceInterface" value="com.wosai.upay.job.service.T9Service"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${merchant-contract-job.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${merchant-contract-job.read_timeout:5000}"/>
    </bean>
    <bean id = "iCustomerRelationFacade" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${crm-customer-relation.server}rpc/relation"></property>
        <property name="serviceInterface" value="facade.ICustomerRelationFacade"></property>
        <property name="serverName" value="crm-customer-relation"/>
        <property name="connectionTimeoutMillis" value="${crm-customer-relation.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${crm-customer-relation.read_timeout:5000}"/>
    </bean>


    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.brand-business.server}/rpc/brand"></property>
        <property name="serviceInterface" value="com.wosai.cua.brand.business.api.facade.BrandFacade"></property>
        <property name="serverName" value="brand-business"/>
        <property name="connectionTimeoutMillis" value="${brand-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${brand-business.read_timeout:5000}"/>
    </bean>

    /**
     * Operation Home Page Service
     */
    @Bean("operationHomePageService")
    public JsonProxyFactoryBean operationHomePageService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(aopBackendService + "/rpc/pay");
        factory.setServiceInterface(com.wosai.aop.backend.service.OperationHomePageService.class);
        factory.setServerName("aop-backend");
        factory.setConnectionTimeoutMillis(500);
        factory.setReadTimeoutMillis(500);
        return factory;
    }
}
