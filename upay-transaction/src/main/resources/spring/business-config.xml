<?xml version="1.0" encoding="UTF-8"?>
<!--
Data and Service layers
-->
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:task="http://www.springframework.org/schema/task" xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd

http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-4.1.xsd

			   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd">

    <bean class="com.wosai.upay.transaction.util.SpringUtil" />

    <bean class="com.wosai.upay.common.helper.UpayMethodValidationPostProcessor">
        <property name="validatedAnnotationType"
                  value="com.wosai.upay.transaction.annotation.CommonTransactionValidated"/>
    </bean>

    <bean id="serviceMethodInterceptor" class="com.wosai.upay.transaction.helper.UpayServiceMethodInterceptor"/>
    <bean class="com.wosai.upay.common.helper.CommonServicePostProcessor">
        <property name="advice" ref="serviceMethodInterceptor"/>
        <property name="annotationTypeClass" value="com.wosai.upay.transaction.annotation.CommonTransactionService"/>
    </bean>
    <bean id = "dBSelectServiceMethodInterceptor" class="com.wosai.upay.transaction.helper.DBSelectServiceMethodInterceptor"></bean>

    <bean class="com.wosai.upay.transaction.helper.DBSelectPostProcessor">
        <property name="advice" ref="dBSelectServiceMethodInterceptor"/>
    </bean>
    <bean class="org.springframework.web.servlet.handler.BeanNameUrlHandlerMapping"/>
    <bean class="com.wosai.upay.transaction.controller.ApolloConfigValidationController"/>

    <context:component-scan base-package="com.wosai.upay.transaction.service, com.wosai.upay.transaction.config"/>
    <mvc:annotation-driven />
    <mvc:default-servlet-handler />
    <mvc:interceptors>
        <bean class="com.wosai.upay.transaction.helper.UpayServiceHandlerInterceptor"/>
        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <bean class="com.wosai.upay.transaction.helper.UpayServiceHandlerInterceptor"/>
        </mvc:interceptor>
    </mvc:interceptors>

    <bean id="myObjectMapper" class="com.wosai.upay.common.helper.MyObjectMapper"/>

    <bean id="healthController" class="com.wosai.upay.transaction.controller.HealthyController"/>
    <bean id="metricsController" class="com.wosai.upay.transaction.controller.MetricsController"/>

    <bean id="rpcErrorResolver" class="com.googlecode.jsonrpc4j.MultipleErrorResolver">
      <constructor-arg>
          <list value-type="com.googlecode.jsonrpc4j.ErrorResolver">
            <value type="com.wosai.upay.transaction.helper.ExceptionBaseErrorResolver">INSTANCE</value>
            <value type="com.googlecode.jsonrpc4j.DefaultErrorResolver">INSTANCE</value>
          </list>
      </constructor-arg>
  </bean>
    
    <!--<context:property-placeholder location="classpath:spring/flavor-${shouqianba.flavor:dev}.properties"/>-->

    <!-- spring的属性加载器，加载properties文件中的属性 -->
    <bean id="propertyConfigurer" class="com.wosai.upay.transaction.util.PropertyUtil">
        <property name="locations">
            <list>
                <value>classpath:spring/flavor-${shouqianba.flavor:default}.properties</value>
                <value>classpath:spring/avatar.properties</value>
            </list>
        </property>
    </bean>

    <import resource="jdbc-dao-config.xml"/>

    <import resource="redis-config.xml"/>

    <bean id="springContextHolder" class="com.wosai.upay.common.util.SpringContextHolder" lazy-init="false"/>

    <bean id="actionExecutor" class="java.util.concurrent.Executors" factory-method="newScheduledThreadPool">
        <constructor-arg value="20"/>
    </bean>

    <bean id="restTemplate" class="org.springframework.web.client.RestTemplate"></bean>
    <bean id="metaCacheUtil" class="com.wosai.upay.transaction.util.MetaCacheUtil"></bean>
    <bean class="com.wosai.upay.transaction.repository.DataRepository">
        <property name="statementConfigDao" ref="statementConfigDao"></property>
        <property name="statementJdbcTemplate" ref="statementJdbcTemplate"></property>
    </bean>
    <bean id="transactionService" class="com.wosai.upay.transaction.service.TransactionServiceImpl"></bean>
    <bean id="transactionServiceV2" class="com.wosai.upay.transaction.service.TransactionServiceV2Impl"></bean>
    <bean id="orderService" class="com.wosai.upay.transaction.service.OrderServiceImpl"/>
    <bean id="statementObjectConfigService" class="com.wosai.upay.transaction.service.StatementObjectConfigServiceImpl"></bean>
    <bean id="exportService" class="com.wosai.upay.transaction.service.ExportServiceImpl">
    </bean>
    <bean id="walletChangeLogHBaseService" class="com.wosai.upay.transaction.service.WalletChangeLogHBaseServiceImpl"></bean>
    <bean id="StatementConfigInterface" class="com.wosai.upay.transaction.service.StatementConfigServiceImpl"></bean>
    <bean id="transactionHBaseServic" class="com.wosai.upay.transaction.service.TransactionHBaseServiceImpl"></bean>
    <bean id="statementConfigService" class="com.wosai.upay.transaction.service.StatementConfigServiceImpl"></bean>
    <bean id="accountBookService" class="com.wosai.upay.transaction.service.api.AccountBookServiceImpl"></bean>
    <bean id="bizExportService" class="com.wosai.upay.transaction.service.api.BizExportServiceImpl"></bean>
    <bean id="accountBookServiceOpen" class="com.wosai.upay.transaction.service.api.AccountBookServiceOpenImpl"></bean>
    <bean id="orderServiceV2" class="com.wosai.upay.transaction.service.api.OrderServiceV2Impl"></bean>
    <bean id="taskLogService" class="com.wosai.upay.transaction.service.TaskLogServiceImpl"></bean>
    <bean id="businessService" class="com.wosai.upay.transaction.service.BusinessService"></bean>
    <bean id="ossFileUploaderService" class="com.wosai.upay.transaction.util.OssFileUploader"></bean>
    <bean id="cacheService" class="com.wosai.upay.transaction.service.CacheServiceImpl"></bean>
    <bean id="upayOrderService" class="com.wosai.upay.transaction.service.UpayOrderServiceImpl"/>
    <bean id="gatewaySupportService" class="com.wosai.upay.transaction.service.GatewaySupportServiceImpl"/>
    <bean id="crmService" class="com.wosai.upay.transaction.service.CrmServiceImpl"/>
    <bean id="checkService" class="com.wosai.upay.transaction.service.CheckServiceImpl"/>
    <bean id="crossMerchantService" class="com.wosai.upay.transaction.service.CrossMerchantServiceImpl"/>
    <bean id="aopRpc" class="com.wosai.upay.transaction.rpc.impl.AopRpcImpl" />
    <bean id="customerTransactionRpc" class="com.wosai.upay.transaction.rpc.impl.CustomerTransactionRpcImpl" />
    <bean id="backendUpayTerminalService" class="com.wosai.upay.transaction.service.BackendUpayTerminalServiceImpl"></bean>
    

    <bean id="jsonRpcSentinelInterceptor" class="com.wosai.upay.transaction.service.config.UpayTransactionJsonRpcInterceptor"/>
<!--    <bean id="elasticsearchProperties" class="com.wosai.upay.transaction.service.config.ElasticsearchProperties">-->
<!--        <property name="instance" value="${es.instance}"/>-->
<!--        <property name="hostname" value="${es.hostname}"/>-->
<!--        <property name="port" value="${es.port}"/>-->
<!--        <property name="connectTimeout" value="${es.connect-timeout}"/>-->
<!--        <property name="socketTimeout" value="${es.socket-timeout}"/>-->
<!--        <property name="connectionRequestTimeout" value="${es.connection-request-timeout}"/>-->
<!--        <property name="maxConnectTotal" value="${es.max-connect-total}"/>-->
<!--        <property name="maxConnectPerRoute" value="${es.max-connect-per-route}"/>-->
<!--    </bean>-->
<!--    <bean id="elasticsearchConfig" class="com.wosai.upay.transaction.service.config.ElasticsearchConfig"/>-->

<!--    <bean id="beanConfig" class="com.wosai.upay.transaction.service.config.BeanConfig"/>-->

    <bean class="com.wosai.upay.transaction.util.OrderUtil"></bean>
    <bean id="statementTransactionDetailUtil" class="com.wosai.upay.transaction.util.StatementTransactionUtil"/>
    <bean id="statementWalletUtils" class="com.wosai.upay.transaction.util.StatementWalletUtils"/>
    <bean id="withdrawExportUtils" class="com.wosai.upay.transaction.util.WithdrawExportUtils"/>


    <bean id="exportHandler" class="com.wosai.upay.transaction.export.base.ExportHandler"/>
    <bean class="com.wosai.upay.transaction.util.ApplicationContextUtil"/>
    <bean class="com.wosai.upay.transaction.util.OdpsUtil"/>

    <bean class="com.wosai.upay.transaction.service.remote.ElasticsearchService"/>

    <bean class="com.wosai.upay.transaction.util.SmtpMailSender">
        <property name="javaMailSender" ref="javaMailSender"></property>
    </bean>



    <!-- 等价于 @EnableAsync， executor指定线程池 -->
    <task:annotation-driven executor="asyncExecutor"/>
    <!-- id指定线程池产生线程名称的前缀 -->
    <task:executor
            id="asyncExecutor"
            pool-size="50-100"
            queue-capacity="100"
            keep-alive="120"
            rejection-policy="CALLER_RUNS"/>

</beans>
